<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Modern SVG Editor - Edit, customize, and export SVG graphics with ease">
    <meta name="keywords" content="SVG editor, vector graphics, design tool, SVG customization, animation">
    <meta name="author" content="Jermesa Studio">
    <title>SVG Editor - Jermesa Studio</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#6366f1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SVG Editor">
    <meta name="msapplication-TileColor" content="#6366f1">
    <meta name="msapplication-config" content="browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Basic favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎨</text></svg>">
    
    <!-- Google Fonts - Open SIL License -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Cookie Consent Banner -->
    <div id="cookieConsent" class="cookie-consent">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
            <div class="cookie-actions">
                <button id="acceptCookies" class="btn btn-primary">Accept</button>
                <a href="https://jermesa.com/privacy-policy/" target="_blank" class="privacy-link">Privacy Policy</a>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="icon-wrapper">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                    </span>
                    SVG Editor
                </h1>
                <div class="header-actions">
                    <button id="themeToggleBtn" class="btn btn-outline btn-icon" title="Toggle Dark Mode">
                        <svg id="lightModeIcon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="5"/>
                            <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"/>
                        </svg>
                        <svg id="darkModeIcon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="display: none;">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                        </svg>
                    </button>
                    <button id="exportBtn" class="btn btn-secondary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" x2="12" y1="15" y2="3"/>
                            </svg>
                        </span>
                        Export
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Input Section -->
            <section class="input-section">
                <div class="section-header">
                    <h2>Input SVG</h2>
                    <div class="input-tabs">
                        <button class="tab-btn active" data-tab="text">Text Input</button>
                        <button class="tab-btn" data-tab="file">File Upload</button>
                    </div>
                </div>
                
                <div class="input-content">
                    <!-- Text Input Tab -->
                    <div id="textTab" class="tab-content active">
                        <textarea id="svgInput" placeholder="Paste your SVG code here..." rows="8"></textarea>
                        <div class="input-actions">
                            <button id="loadSampleBtn" class="btn btn-outline">Load Sample</button>
                            <button id="clearInputBtn" class="btn btn-outline">Clear</button>
                        </div>
                    </div>
                    
                    <!-- File Upload Tab -->
                    <div id="fileTab" class="tab-content">
                        <div class="file-upload-area" id="fileUploadArea">
                            <div class="upload-content">
                                <span class="upload-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="17,8 12,3 7,8"/>
                                        <line x1="12" x2="12" y1="3" y2="15"/>
                                    </svg>
                                </span>
                                <p>Drop SVG file here or <span class="upload-link">browse</span></p>
                                <input type="file" id="fileInput" accept=".svg" hidden>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Preview and Editor Section -->
            <section class="editor-section">
                <div class="editor-layout">
                    <!-- SVG Preview -->
                    <div class="preview-panel">
                        <div class="panel-header">
                            <h3>Preview</h3>
                            <div class="preview-controls">
                                <!-- Animation Playback Controls -->
                                <div class="animation-playback-preview" id="animationPlaybackPreview" style="display: none;">
                                    <button id="playAnimationPreviewBtn" class="btn btn-primary btn-icon" title="Play Animation">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="5,3 19,12 5,21 5,3"/>
                                        </svg>
                                    </button>
                                    <button id="pauseAnimationPreviewBtn" class="btn btn-secondary btn-icon" title="Pause Animation">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="4" height="16" x="6" y="4"/>
                                            <rect width="4" height="16" x="14" y="4"/>
                                        </svg>
                                    </button>
                                    <button id="stopAnimationPreviewBtn" class="btn btn-outline btn-icon" title="Stop Animation">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect width="14" height="14" x="5" y="5" rx="2"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- Solo View Controls -->
                                <button id="soloViewBtn" class="btn btn-outline btn-icon" title="Solo View Mode">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                        <circle cx="12" cy="12" r="3"/>
                                    </svg>
                                </button>
                                <button id="showAllBtn" class="btn btn-outline btn-icon" title="Show All Elements" style="display: none;">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                        <circle cx="9" cy="9" r="2"/>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                    </svg>
                                </button>

                                <!-- Zoom Controls -->
                                <button id="zoomInBtn" class="btn btn-icon" title="Zoom In">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="11" x2="11" y1="8" y2="14"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button id="zoomOutBtn" class="btn btn-icon" title="Zoom Out">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button id="resetZoomBtn" class="btn btn-icon" title="Reset Zoom">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                        <path d="M21 3v5h-5"/>
                                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                        <path d="M8 16H3v5"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="preview-container" id="previewContainer">
                            <div class="preview-placeholder">
                                <span class="placeholder-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                        <circle cx="9" cy="9" r="2"/>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                    </svg>
                                </span>
                                <p>SVG preview will appear here</p>
                            </div>
                        </div>
                    </div>

                    <!-- Editing Controls -->
                    <div class="controls-panel">
                        <div class="panel-header">
                            <h3>Edit Elements</h3>
                            <div class="header-actions">
                                <button id="randomizeColorsBtn" class="btn btn-primary btn-sm">
                                    <span class="icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                            <path d="M21 3v5h-5"/>
                                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                            <path d="M8 16H3v5"/>
                                        </svg>
                                    </span>
                                    Colors
                                </button>
                                <button id="randomizeAnimationsBtn" class="btn btn-secondary btn-sm">
                                    <span class="icon">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="5,3 19,12 5,21 5,3"/>
                                        </svg>
                                    </span>
                                    Animate
                                </button>
                            </div>
                        </div>

                        <!-- Control Tabs -->
                        <div class="control-tabs">
                            <button class="control-tab-btn active" data-tab="styling">Styling</button>
                            <button class="control-tab-btn" data-tab="animation">Animation</button>
                        </div>

                        <div class="controls-content" id="controlsContent">
                            <div class="no-elements">
                                <p>Load an SVG to start editing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Animation Controls Section -->
            <section class="animation-section" id="animationSection" style="display: none;">
                <div class="animation-layout">
                    <!-- Global Animation Controls -->
                    <div class="global-animation-panel">
                        <div class="panel-header">
                            <h3>Global Animation</h3>
                        </div>
                        <div class="global-animation-content">
                            <div class="animation-preset-section">
                                <h4>Whole SVG Animation</h4>
                                <div class="preset-selector">
                                    <select id="globalAnimationPreset">
                                        <option value="">Select Animation</option>
                                    </select>
                                    <button id="applyGlobalAnimationBtn" class="btn btn-primary">Apply</button>
                                </div>
                                <div class="animation-settings">
                                    <div class="setting-group">
                                        <label>Duration (ms):</label>
                                        <input type="number" id="globalDuration" value="1000" min="100" max="10000" step="100">
                                    </div>
                                    <div class="setting-group">
                                        <label>Delay (ms):</label>
                                        <input type="number" id="globalDelay" value="0" min="0" max="5000" step="100">
                                    </div>
                                    <div class="setting-group">
                                        <label>Iterations:</label>
                                        <select id="globalIterations">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="5">5</option>
                                            <option value="infinite">Infinite</option>
                                        </select>
                                    </div>
                                    <div class="setting-group">
                                        <label>Direction:</label>
                                        <select id="globalDirection">
                                            <option value="normal">Normal</option>
                                            <option value="reverse">Reverse</option>
                                            <option value="alternate">Alternate</option>
                                            <option value="alternate-reverse">Alternate Reverse</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Animation Timeline -->
                    <div class="timeline-panel">
                        <div class="panel-header">
                            <h3>Animation Timeline</h3>
                            <div class="timeline-controls">
                                <button id="clearAllAnimationsBtn" class="btn btn-outline btn-sm">Clear All</button>
                                <button id="randomizeAllAnimationsBtn" class="btn btn-secondary btn-sm">Random All</button>
                            </div>
                        </div>
                        <div class="timeline-content" id="timelineContent">
                            <div class="timeline-placeholder">
                                <p>Add animations to see timeline</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-left">
                    <p>Created by <a href="https://www.jermesa.com" target="_blank" rel="noopener">Jermesa Studio</a></p>
                </div>
                <div class="footer-right">
                    <p>Fonts: <a href="https://fonts.google.com/specimen/Inter" target="_blank" rel="noopener">Inter</a> & <a href="https://fonts.google.com/specimen/JetBrains+Mono" target="_blank" rel="noopener">JetBrains Mono</a> (Open SIL License)</p>
                    <a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener">Privacy Policy</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Export SVG</h3>
                <button class="modal-close" id="closeExportModal">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="m18 6-12 12"/>
                        <path d="m6 6 12 12"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <!-- Export Type Selection -->
                <div class="export-type-selection">
                    <h4>Export Type</h4>
                    <div class="export-type-tabs">
                        <button class="export-type-btn active" data-type="static">Static SVG</button>
                        <button class="export-type-btn" data-type="animated">Animated SVG</button>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="export-options">
                    <button id="downloadSvgBtn" class="btn btn-primary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" x2="12" y1="15" y2="3"/>
                            </svg>
                        </span>
                        Download SVG File
                    </button>
                    <button id="copySvgBtn" class="btn btn-secondary">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"/>
                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/>
                            </svg>
                        </span>
                        Copy to Clipboard
                    </button>
                    <button id="previewAnimationBtn" class="btn btn-outline" style="display: none;">
                        <span class="icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="5,3 19,12 5,21 5,3"/>
                            </svg>
                        </span>
                        Preview Animation
                    </button>
                </div>

                <!-- Animation Export Settings -->
                <div id="animationExportSettings" class="animation-export-settings" style="display: none;">
                    <h4>Animation Settings</h4>
                    <div class="export-animation-options">
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="includeCSS" checked>
                                Include CSS animations
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="includeJS">
                                Include JavaScript controls
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="autoPlay" checked>
                                Auto-play animations
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Export Code -->
                <div class="export-code">
                    <label for="exportTextarea">SVG Code:</label>
                    <textarea id="exportTextarea" readonly rows="10"></textarea>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>

    <!-- PWA Service Worker Registration -->
    <script>
        // Register service worker for PWA functionality (disabled temporarily to avoid errors)
        if (false && 'serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);

                        // Check for updates
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New content is available, show update notification
                                    showUpdateNotification();
                                }
                            });
                        });
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Show update notification
        function showUpdateNotification() {
            if (window.svgEditor) {
                window.svgEditor.showNotification('A new version is available! Refresh to update.', 'info');
            }
        }

        // Install prompt for PWA
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            // Prevent Chrome 67 and earlier from automatically showing the prompt
            e.preventDefault();
            // Stash the event so it can be triggered later
            deferredPrompt = e;

            // Show install button or notification
            showInstallPrompt();
        });

        function showInstallPrompt() {
            // Create install notification
            const installNotification = document.createElement('div');
            installNotification.className = 'install-prompt';
            installNotification.innerHTML = `
                <div class="install-content">
                    <p>Install SVG Editor for a better experience!</p>
                    <div class="install-actions">
                        <button id="installBtn" class="btn btn-primary btn-sm">Install</button>
                        <button id="dismissInstallBtn" class="btn btn-outline btn-sm">Not now</button>
                    </div>
                </div>
            `;

            document.body.appendChild(installNotification);

            // Handle install button click
            document.getElementById('installBtn').addEventListener('click', () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the install prompt');
                        } else {
                            console.log('User dismissed the install prompt');
                        }
                        deferredPrompt = null;
                        document.body.removeChild(installNotification);
                    });
                }
            });

            // Handle dismiss button click
            document.getElementById('dismissInstallBtn').addEventListener('click', () => {
                document.body.removeChild(installNotification);
                deferredPrompt = null;
            });

            // Auto-dismiss after 10 seconds
            setTimeout(() => {
                if (document.body.contains(installNotification)) {
                    document.body.removeChild(installNotification);
                }
            }, 10000);
        }

        // Handle app installed event
        window.addEventListener('appinstalled', (evt) => {
            console.log('SVG Editor was installed');
            if (window.svgEditor) {
                window.svgEditor.showSuccess('SVG Editor installed successfully!');
            }
        });
    </script>
</body>
</html>
