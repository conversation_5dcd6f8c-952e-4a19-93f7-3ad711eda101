<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Editor Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .test-svg { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>SVG Editor Test Page</h1>
    
    <div class="test-section">
        <h2>Test SVG 1: Simple Shapes</h2>
        <div class="test-svg">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
                <circle cx="100" cy="100" r="50" fill="#ff0000" stroke="#000000" stroke-width="2"/>
                <rect x="50" y="50" width="100" height="100" fill="#00ff00" stroke="#0000ff" stroke-width="3" opacity="0.7"/>
            </svg>
        </div>
        <button onclick="testSVG1()">Test This SVG</button>
    </div>
    
    <div class="test-section">
        <h2>Test SVG 2: Complex with Gradients</h2>
        <div class="test-svg">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <circle cx="100" cy="100" r="80" fill="url(#grad1)" stroke="#334155" stroke-width="3"/>
                <rect x="70" y="70" width="60" height="60" fill="#f59e0b" stroke="#dc2626" stroke-width="2" rx="10"/>
                <polygon points="100,50 120,90 80,90" fill="#10b981" stroke="#065f46" stroke-width="2"/>
                <text x="100" y="160" text-anchor="middle" fill="#1f2937" font-family="Arial" font-size="16" font-weight="bold">Sample SVG</text>
            </svg>
        </div>
        <button onclick="testSVG2()">Test This SVG</button>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="testResults"></div>
    </div>
    
    <script>
        function testSVG1() {
            const svg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
                <circle cx="100" cy="100" r="50" fill="#ff0000" stroke="#000000" stroke-width="2"/>
                <rect x="50" y="50" width="100" height="100" fill="#00ff00" stroke="#0000ff" stroke-width="3" opacity="0.7"/>
            </svg>`;
            
            // Copy to clipboard for easy testing
            navigator.clipboard.writeText(svg).then(() => {
                document.getElementById('testResults').innerHTML += '<p>✅ Test SVG 1 copied to clipboard - paste it in the main app</p>';
            });
        }
        
        function testSVG2() {
            const svg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
                <defs>
                    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <circle cx="100" cy="100" r="80" fill="url(#grad1)" stroke="#334155" stroke-width="3"/>
                <rect x="70" y="70" width="60" height="60" fill="#f59e0b" stroke="#dc2626" stroke-width="2" rx="10"/>
                <polygon points="100,50 120,90 80,90" fill="#10b981" stroke="#065f46" stroke-width="2"/>
                <text x="100" y="160" text-anchor="middle" fill="#1f2937" font-family="Arial" font-size="16" font-weight="bold">Sample SVG</text>
            </svg>`;
            
            // Copy to clipboard for easy testing
            navigator.clipboard.writeText(svg).then(() => {
                document.getElementById('testResults').innerHTML += '<p>✅ Test SVG 2 copied to clipboard - paste it in the main app</p>';
            });
        }
        
        // Instructions
        document.getElementById('testResults').innerHTML = `
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Click a "Test This SVG" button to copy the SVG code</li>
                <li>Open the main SVG Editor (index.html)</li>
                <li>Paste the SVG code in the text input area</li>
                <li>Try changing element colors, stroke width, and opacity</li>
                <li>Export the SVG and check if all elements are preserved</li>
            </ol>
        `;
    </script>
</body>
</html>
