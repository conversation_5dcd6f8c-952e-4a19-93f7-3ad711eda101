<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="512" height="512" rx="80" ry="80" fill="url(#grad1)"/>
  
  <!-- Main SVG Icon -->
  <g transform="translate(128, 128)">
    <!-- Document background -->
    <rect x="0" y="0" width="256" height="256" rx="20" ry="20" fill="white" opacity="0.95"/>
    
    <!-- SVG Elements -->
    <circle cx="128" cy="100" r="40" fill="url(#grad2)" opacity="0.8"/>
    <rect x="80" y="140" width="96" height="60" rx="8" ry="8" fill="#10b981" opacity="0.8"/>
    <polygon points="128,60 160,120 96,120" fill="#8b5cf6" opacity="0.8"/>
    
    <!-- Edit icon overlay -->
    <g transform="translate(180, 180)">
      <circle cx="32" cy="32" r="32" fill="#6366f1"/>
      <path d="M20 20 L32 8 L40 16 L28 28 Z" fill="white" stroke="white" stroke-width="2"/>
      <path d="M20 28 L20 44 L36 44 L28 28 Z" fill="white"/>
    </g>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="80" cy="80" r="8" fill="white" opacity="0.3"/>
  <circle cx="432" cy="432" r="12" fill="white" opacity="0.2"/>
  <circle cx="450" cy="80" r="6" fill="white" opacity="0.4"/>
</svg>
