<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 10px; display: inline-block; text-align: center; }
        .icon-preview canvas { border: 1px solid #ccc; margin-bottom: 5px; }
        button { padding: 10px 20px; margin: 10px; }
    </style>
</head>
<body>
    <h1>PWA Icon Generator for SVG Editor</h1>
    <p>This tool generates all the required PWA icons from the base SVG icon.</p>
    
    <button onclick="generateAllIcons()">Generate All Icons</button>
    <button onclick="downloadAllIcons()">Download All Icons</button>
    
    <div id="iconContainer"></div>
    
    <script>
        const iconSizes = [
            { size: 16, name: 'icon-16x16.png' },
            { size: 32, name: 'icon-32x32.png' },
            { size: 72, name: 'icon-72x72.png' },
            { size: 96, name: 'icon-96x96.png' },
            { size: 128, name: 'icon-128x128.png' },
            { size: 144, name: 'icon-144x144.png' },
            { size: 152, name: 'icon-152x152.png' },
            { size: 180, name: 'icon-180x180.png' },
            { size: 192, name: 'icon-192x192.png' },
            { size: 384, name: 'icon-384x384.png' },
            { size: 512, name: 'icon-512x512.png' }
        ];
        
        let generatedIcons = [];
        
        function generateAllIcons() {
            const container = document.getElementById('iconContainer');
            container.innerHTML = '';
            generatedIcons = [];
            
            // Load the SVG
            fetch('icons/app-icon.svg')
                .then(response => response.text())
                .then(svgText => {
                    iconSizes.forEach(iconInfo => {
                        generateIcon(svgText, iconInfo.size, iconInfo.name, container);
                    });
                })
                .catch(error => {
                    console.error('Error loading SVG:', error);
                    alert('Error loading SVG file. Make sure app-icon.svg exists in the icons folder.');
                });
        }
        
        function generateIcon(svgText, size, filename, container) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            const img = new Image();
            const svgBlob = new Blob([svgText], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // Convert to PNG
                canvas.toBlob(function(blob) {
                    generatedIcons.push({ blob, filename });
                    
                    // Create preview
                    const preview = document.createElement('div');
                    preview.className = 'icon-preview';
                    preview.innerHTML = `
                        <canvas width="${size}" height="${size}"></canvas>
                        <div>${filename} (${size}x${size})</div>
                    `;
                    
                    const previewCanvas = preview.querySelector('canvas');
                    const previewCtx = previewCanvas.getContext('2d');
                    previewCtx.drawImage(canvas, 0, 0);
                    
                    container.appendChild(preview);
                }, 'image/png');
                
                URL.revokeObjectURL(url);
            };
            
            img.src = url;
        }
        
        function downloadAllIcons() {
            if (generatedIcons.length === 0) {
                alert('Please generate icons first!');
                return;
            }
            
            generatedIcons.forEach(icon => {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(icon.blob);
                link.download = icon.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            });
            
            alert(`Downloaded ${generatedIcons.length} icons!`);
        }
        
        // Auto-generate icons on page load
        window.addEventListener('load', () => {
            setTimeout(generateAllIcons, 1000);
        });
    </script>
</body>
</html>
