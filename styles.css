/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;

    /* Light Theme Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Theme Variables */
    --bg-primary: var(--gray-50);
    --bg-secondary: var(--white);
    --bg-tertiary: var(--gray-100);
    --text-primary: var(--gray-800);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-300);
    --shadow-color: rgba(0, 0, 0, 0.1);
}
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    /* Dark Theme Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-primary: #334155;
    --border-secondary: #475569;
    --shadow-color: rgba(0, 0, 0, 0.3);

    /* Adjust other colors for dark theme */
    --gray-50: #1e293b;
    --gray-100: #334155;
    --gray-200: #475569;
    --gray-300: #64748b;

    /* Primary colors remain the same but adjust hover states */
    --primary-hover: #7c3aed;
}

/* Ensure proper contrast in dark mode */
[data-theme="dark"] .btn-outline {
    border-color: var(--border-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .btn-outline:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--primary-color);
}

/* Dark mode input styling */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--primary-color);
    background-color: var(--bg-secondary);
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    min-height: 100vh;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Cookie Consent Banner */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-md);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
}

.cookie-consent.show {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.cookie-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

.privacy-link {
    color: var(--gray-300);
    text-decoration: underline;
    font-size: 0.875rem;
}

.privacy-link:hover {
    color: var(--white);
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.app-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-md);
}

.icon-wrapper svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.btn-outline {
    background: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-700);
}

.btn-icon {
    padding: var(--spacing-sm);
    width: 2.5rem;
    height: 2.5rem;
    justify-content: center;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.btn .icon svg {
    width: 1rem;
    height: 1rem;
}

/* Main Content */
.app-main {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-md);
    width: 100%;
}

/* Input Section */
.input-section {
    margin-bottom: var(--spacing-2xl);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.input-tabs {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn.active {
    background: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
    color: var(--gray-800);
}

.input-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

#svgInput {
    width: 100%;
    min-height: 200px;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: border-color var(--transition-fast);
}

#svgInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.input-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

/* File Upload */
.file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-icon {
    width: 3rem;
    height: 3rem;
    color: var(--gray-400);
}

.upload-icon svg {
    width: 100%;
    height: 100%;
}

.upload-link {
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
}

.upload-link:hover {
    text-decoration: underline;
}

/* Editor Section */
.editor-section {
    margin-bottom: var(--spacing-2xl);
}

.editor-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-xl);
}

.preview-panel,
.controls-panel {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.panel-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.header-actions {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.preview-controls {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
    flex-wrap: wrap;
}

.animation-playback-preview {
    display: flex;
    gap: var(--spacing-xs);
    margin-right: var(--spacing-sm);
    padding-right: var(--spacing-sm);
    border-right: 1px solid var(--gray-300);
}

.preview-container {
    padding: var(--spacing-lg);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    position: relative;
    overflow: auto;
    border: 1px solid var(--gray-100);
    background-image:
        linear-gradient(45deg, var(--gray-50) 25%, transparent 25%),
        linear-gradient(-45deg, var(--gray-50) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, var(--gray-50) 75%),
        linear-gradient(-45deg, transparent 75%, var(--gray-50) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--gray-400);
    text-align: center;
}

.placeholder-icon {
    width: 4rem;
    height: 4rem;
}

.placeholder-icon svg {
    width: 100%;
    height: 100%;
}

.controls-content {
    padding: var(--spacing-lg);
    max-height: 600px;
    overflow-y: auto;
}

.no-elements {
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-2xl) 0;
}

/* Footer */
.app-footer {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
}

.footer-content a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
}

.footer-content a:hover {
    text-decoration: underline;
}

.footer-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform var(--transition-normal), background-color var(--transition-normal);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-close svg {
    width: 1.25rem;
    height: 1.25rem;
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: calc(90vh - 120px);
}

.export-options {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.export-code label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

#exportTextarea {
    width: 100%;
    min-height: 200px;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    background: var(--gray-50);
}

/* Export Modal Enhancements */
.export-type-selection {
    margin-bottom: var(--spacing-lg);
}

.export-type-selection h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.export-type-tabs {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
}

.export-type-btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.export-type-btn.active {
    background: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.export-type-btn:hover:not(.active) {
    color: var(--gray-800);
}

.animation-export-settings {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    background: var(--gray-50);
}

.animation-export-settings h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.export-animation-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.export-animation-options .setting-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.export-animation-options label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--gray-700);
    cursor: pointer;
}

.export-animation-options input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

/* PWA Install Prompt */
.install-prompt {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    z-index: 1100;
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.install-content p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.install-actions {
    display: flex;
    gap: var(--spacing-sm);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Animation Preview Styles */
.animation-preview {
    transform-origin: center center;
    animation-fill-mode: both;
}

/* Ensure animations are visible during preview */
svg .animation-preview {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .editor-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .controls-panel {
        order: -1;
    }
}

@media (max-width: 768px) {
    .app-main {
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .app-title {
        font-size: 1.25rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .footer-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .export-options {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .input-tabs {
        width: 100%;
    }

    .tab-btn {
        flex: 1;
        text-align: center;
    }

    .input-actions {
        flex-direction: column;
    }

    .preview-controls {
        flex-wrap: wrap;
    }
}

/* Control Panel Styles */
.control-section {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.control-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.control-section h5 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

/* Color Controls */
.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.color-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    background: var(--white);
}

.color-item input[type="color"] {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
}

.color-item label {
    font-size: 0.75rem;
    color: var(--gray-600);
    font-family: var(--font-mono);
}

.preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(2rem, 1fr));
    gap: var(--spacing-xs);
}

.preset-color {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--white);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.preset-color:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

/* Element Controls */
.elements-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.element-item {
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    overflow: hidden;
    transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

.element-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-tertiary);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.element-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.solo-element-btn {
    padding: var(--spacing-xs);
    min-width: auto;
    height: auto;
}

.solo-element-btn svg {
    width: 0.875rem;
    height: 0.875rem;
}

.element-tag {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
}

.element-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.element-toggle:hover {
    color: var(--gray-600);
    background: var(--gray-100);
}

.element-toggle svg {
    width: 1rem;
    height: 1rem;
    transition: transform var(--transition-fast);
}

.element-controls {
    padding: var(--spacing-md);
    display: block;
    border-top: 1px solid var(--gray-200);
}

.control-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.control-group:last-child {
    margin-bottom: 0;
}

.control-group label {
    font-size: 0.875rem;
    color: var(--gray-700);
    min-width: 80px;
    font-weight: 500;
}

.control-group input[type="color"] {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
}

.control-group input[type="range"] {
    flex: 1;
    margin: 0 var(--spacing-sm);
}

.range-value {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-family: var(--font-mono);
    min-width: 2rem;
    text-align: right;
}

/* Notifications */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    color: var(--white);
    font-weight: 500;
    z-index: 1100;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: var(--success-color);
}

.notification-error {
    background: var(--danger-color);
}

.notification-info {
    background: var(--primary-color);
}

.notification-warning {
    background: var(--warning-color);
}

/* Control Tabs */
.control-tabs {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs);
    margin: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
}

.control-tab-btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.control-tab-btn.active {
    background: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.control-tab-btn:hover:not(.active) {
    color: var(--gray-800);
}

/* Animation Section */
.animation-section {
    margin-bottom: var(--spacing-2xl);
}

.animation-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.global-animation-panel,
.timeline-panel {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.animation-playback {
    display: flex;
    gap: var(--spacing-xs);
}

.global-animation-content,
.timeline-content {
    padding: var(--spacing-lg);
}

.animation-preset-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.preset-selector {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.preset-selector select {
    flex: 1;
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.animation-settings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.setting-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-700);
}

.setting-group input,
.setting-group select {
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.timeline-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.timeline-placeholder {
    text-align: center;
    color: var(--gray-500);
    padding: var(--spacing-2xl) 0;
}

/* Animation Timeline Items */
.timeline-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    background: var(--gray-50);
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-element {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
}

.timeline-animation {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.timeline-duration {
    font-size: 0.75rem;
    color: var(--gray-500);
    font-family: var(--font-mono);
}

.timeline-actions {
    display: flex;
    gap: var(--spacing-xs);
}

/* Element Animation Controls */
.element-animation-controls {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--gray-200);
}

.animation-preset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.animation-preset-btn {
    padding: var(--spacing-sm);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
}

.animation-preset-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--gray-50);
}

.animation-preset-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.animation-category {
    margin-bottom: var(--spacing-lg);
}

.animation-category h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Additional responsive adjustments */
@media (max-width: 1024px) {
    .animation-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .timeline-panel {
        order: -1;
    }
}

@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: 1fr;
    }

    .preset-grid {
        grid-template-columns: repeat(auto-fill, minmax(1.5rem, 1fr));
    }

    .preset-color {
        width: 1.5rem;
        height: 1.5rem;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xs);
    }

    .control-group label {
        min-width: auto;
    }

    .notification {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }

    .animation-settings {
        grid-template-columns: 1fr;
    }

    .preset-selector {
        flex-direction: column;
    }

    .animation-preset-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .timeline-actions {
        align-self: flex-end;
    }
}
